{"name": "@kit/baselime", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./server": "./src/server.ts", "./client": "./src/client.ts", "./instrumentation": "./src/instrumentation.ts", "./provider": "./src/components/provider.tsx"}, "dependencies": {"@baselime/node-opentelemetry": "^0.5.8", "@baselime/react-rum": "^0.3.1", "@kit/monitoring-core": "workspace:*"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/react": "19.1.4", "react": "19.1.0", "zod": "^3.25.7"}, "typesVersions": {"*": {"*": ["src/*"]}}}