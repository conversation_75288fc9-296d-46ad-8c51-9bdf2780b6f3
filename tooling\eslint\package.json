{"name": "@kit/eslint-config", "version": "0.2.0", "private": true, "type": "module", "files": ["./apps.js", "./base.js", "./nextjs.js"], "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check \"**/*.{js,json}\""}, "dependencies": {"@next/eslint-plugin-next": "15.3.2", "@types/eslint": "9.6.1", "eslint-config-next": "15.3.1", "eslint-config-turbo": "^2.5.3", "typescript-eslint": "8.32.1"}, "devDependencies": {"@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "eslint": "^9.27.0", "typescript": "^5.8.3"}, "prettier": "@kit/prettier-config"}