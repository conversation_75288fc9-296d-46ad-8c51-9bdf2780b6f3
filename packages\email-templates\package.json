{"name": "@kit/email-templates", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "dependencies": {"@react-email/components": "0.0.41"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/i18n": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*"}, "typesVersions": {"*": {"*": ["src/*"]}}}