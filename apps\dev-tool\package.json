{"name": "dev-tool", "version": "0.1.0", "private": true, "scripts": {"clean": "git clean -xdf .next .turbo node_modules", "dev": "next dev --turbo --port=3010 | pino-pretty -c", "format": "prettier --check --write \"**/*.{js,cjs,mjs,ts,tsx,md,json}\""}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "5.76.1", "ai": "4.3.16", "lucide-react": "^0.511.0", "next": "15.3.2", "nodemailer": "^7.0.3", "react": "19.1.0", "react-dom": "19.1.0", "rxjs": "^7.8.2"}, "devDependencies": {"@kit/email-templates": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.19", "@types/nodemailer": "6.4.17", "@types/react": "19.1.4", "@types/react-dom": "19.1.5", "babel-plugin-react-compiler": "19.1.0-rc.2", "pino-pretty": "^13.0.0", "react-hook-form": "^7.56.4", "tailwindcss": "4.1.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "zod": "^3.25.7"}, "prettier": "@kit/prettier-config", "browserslist": ["last 1 versions", "> 0.7%", "not dead"]}