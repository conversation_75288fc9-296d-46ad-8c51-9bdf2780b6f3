{"name": "@kit/monitoring", "private": true, "sideEffects": false, "version": "0.1.0", "scripts": {"clean": "git clean -xdf ../.turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint ..", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./server": "./src/server.ts", "./instrumentation": "./src/instrumentation.ts", "./hooks": "./src/hooks/index.ts", "./components": "./src/components/index.ts"}, "devDependencies": {"@kit/baselime": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/monitoring-core": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/sentry": "workspace:*", "@kit/shared": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/react": "19.1.4", "react": "19.1.0", "zod": "^3.25.7"}, "typesVersions": {"*": {"*": ["src/*"]}}}