{"name": "@kit/posthog", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./server": "./src/server.ts"}, "dependencies": {"posthog-js": "1.194.2", "posthog-node": "4.3.0"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/node": "^22.15.19"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}