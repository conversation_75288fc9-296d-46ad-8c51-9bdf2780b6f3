{"name": "@kit/sentry", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./provider": "./src/components/provider.tsx", "./config/client": "./src/sentry.client.config.ts", "./config/server": "./src/sentry.client.server.ts"}, "dependencies": {"@sentry/nextjs": "^9.20.0", "import-in-the-middle": "1.13.2"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/monitoring-core": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/react": "19.1.4", "react": "19.1.0"}, "typesVersions": {"*": {"*": ["src/*"]}}}