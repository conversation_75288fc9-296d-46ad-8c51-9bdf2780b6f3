{"name": "@kit/nodemailer", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "dependencies": {"nodemailer": "^7.0.3"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/mailers-shared": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/nodemailer": "6.4.17", "zod": "^3.25.7"}, "typesVersions": {"*": {"*": ["src/*"]}}}