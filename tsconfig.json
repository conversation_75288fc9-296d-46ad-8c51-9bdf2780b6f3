{
  "compilerOptions": {
    "target": "es2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "checkJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "noUncheckedIndexedAccess": true,
    "baseUrl": ".",
    "paths": {
      "@kit/*": ["packages/*/src"],
      "@kit/payload": ["packages/cms/payload/src"],
      "@kit/payload/renderer": ["packages/cms/payload/src/renderer"]
    }
  },
  "references": [
    // Packages (assuming these are standard workspaces with tsconfig.json)
    { "path": "./packages/cms/payload" }
    // Other references removed until we fix their composite settings
  ],
  "exclude": ["node_modules", "apps/payload_legacy"] // Also exclude payload_legacy
}
